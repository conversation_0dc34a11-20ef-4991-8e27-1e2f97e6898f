export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  public: {
    Tables: {
      emergency_contacts: {
        Row: {
          created_at: string | null;
          full_name: string;
          id: string;
          primary_phone: string;
          relationship: string;
          secondary_phone: string | null;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          created_at?: string | null;
          full_name: string;
          id?: string;
          primary_phone: string;
          relationship: string;
          secondary_phone?: string | null;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          created_at?: string | null;
          full_name?: string;
          id?: string;
          primary_phone?: string;
          relationship?: string;
          secondary_phone?: string | null;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "emergency_contacts_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
        ];
      };
      food_logs: {
        Row: {
          calories: number;
          created_at: string | null;
          fat: number;
          food_name: string;
          id: string;
          is_liver_friendly: boolean;
          logged_at: string | null;
          meal_type: string;
          protein: number;
          quantity: string;
          sodium: number;
          user_id: string;
          warnings: string[] | null;
        };
        Insert: {
          calories: number;
          created_at?: string | null;
          fat: number;
          food_name: string;
          id?: string;
          is_liver_friendly?: boolean;
          logged_at?: string | null;
          meal_type: string;
          protein: number;
          quantity: string;
          sodium: number;
          user_id: string;
          warnings?: string[] | null;
        };
        Update: {
          calories?: number;
          created_at?: string | null;
          fat?: number;
          food_name?: string;
          id?: string;
          is_liver_friendly?: boolean;
          logged_at?: string | null;
          meal_type?: string;
          protein?: number;
          quantity?: string;
          sodium?: number;
          user_id?: string;
          warnings?: string[] | null;
        };
        Relationships: [
          {
            foreignKeyName: "food_logs_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
        ];
      };
      medical_profiles: {
        Row: {
          age: number;
          created_at: string | null;
          diagnosis_date: string;
          disease_stage: string;
          healthcare_provider: string;
          height: number;
          id: string;
          liver_condition: string;
          updated_at: string | null;
          user_id: string;
          weight: number;
        };
        Insert: {
          age: number;
          created_at?: string | null;
          diagnosis_date: string;
          disease_stage: string;
          healthcare_provider: string;
          height: number;
          id?: string;
          liver_condition: string;
          updated_at?: string | null;
          user_id: string;
          weight: number;
        };
        Update: {
          age?: number;
          created_at?: string | null;
          diagnosis_date?: string;
          disease_stage?: string;
          healthcare_provider?: string;
          height?: number;
          id?: string;
          liver_condition?: string;
          updated_at?: string | null;
          user_id?: string;
          weight?: number;
        };
        Relationships: [
          {
            foreignKeyName: "medical_profiles_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
        ];
      };
      medications: {
        Row: {
          created_at: string | null;
          dosage: string;
          frequency: string;
          id: string;
          name: string;
          prescribing_doctor: string;
          special_instructions: string | null;
          start_date: string;
          timing_requirements: string[] | null;
          unit: string;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          created_at?: string | null;
          dosage: string;
          frequency: string;
          id?: string;
          name: string;
          prescribing_doctor: string;
          special_instructions?: string | null;
          start_date: string;
          timing_requirements?: string[] | null;
          unit: string;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          created_at?: string | null;
          dosage?: string;
          frequency?: string;
          id?: string;
          name?: string;
          prescribing_doctor?: string;
          special_instructions?: string | null;
          start_date?: string;
          timing_requirements?: string[] | null;
          unit?: string;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "medications_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
        ];
      };
      onboarding_progress: {
        Row: {
          completed_steps: number[] | null;
          created_at: string | null;
          current_step: number | null;
          id: string;
          is_completed: boolean | null;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          completed_steps?: number[] | null;
          created_at?: string | null;
          current_step?: number | null;
          id?: string;
          is_completed?: boolean | null;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          completed_steps?: number[] | null;
          created_at?: string | null;
          current_step?: number | null;
          id?: string;
          is_completed?: boolean | null;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "onboarding_progress_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
        ];
      };
      profiles: {
        Row: {
          avatar_url: string | null;
          created_at: string | null;
          date_of_birth: string | null;
          email: string;
          first_name: string;
          id: string;
          last_name: string;
          phone: string | null;
          updated_at: string | null;
        };
        Insert: {
          avatar_url?: string | null;
          created_at?: string | null;
          date_of_birth?: string | null;
          email: string;
          first_name: string;
          id: string;
          last_name: string;
          phone?: string | null;
          updated_at?: string | null;
        };
        Update: {
          avatar_url?: string | null;
          created_at?: string | null;
          date_of_birth?: string | null;
          email?: string;
          first_name?: string;
          id?: string;
          last_name?: string;
          phone?: string | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      secondary_conditions: {
        Row: {
          condition: string;
          created_at: string | null;
          id: string;
          user_id: string;
        };
        Insert: {
          condition: string;
          created_at?: string | null;
          id?: string;
          user_id: string;
        };
        Update: {
          condition?: string;
          created_at?: string | null;
          id?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "secondary_conditions_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
        ];
      };
      test_results: {
        Row: {
          created_at: string | null;
          date_conducted: string;
          id: string;
          lab_name: string;
          result: string;
          test_type: string;
          unit: string;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          created_at?: string | null;
          date_conducted: string;
          id?: string;
          lab_name: string;
          result: string;
          test_type: string;
          unit: string;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          created_at?: string | null;
          date_conducted?: string;
          id?: string;
          lab_name?: string;
          result?: string;
          test_type?: string;
          unit?: string;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "test_results_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  } ? keyof (
      & Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
      & Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"]
    )
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database } ? (
    & Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    & Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"]
  )[TableName] extends {
    Row: infer R;
  } ? R
  : never
  : DefaultSchemaTableNameOrOptions extends keyof (
    & DefaultSchema["Tables"]
    & DefaultSchema["Views"]
  ) ? (
      & DefaultSchema["Tables"]
      & DefaultSchema["Views"]
    )[DefaultSchemaTableNameOrOptions] extends {
      Row: infer R;
    } ? R
    : never
  : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  } ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][
    TableName
  ] extends {
    Insert: infer I;
  } ? I
  : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Insert: infer I;
    } ? I
    : never
  : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  } ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][
    TableName
  ] extends {
    Update: infer U;
  } ? U
  : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Update: infer U;
    } ? U
    : never
  : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  } ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
  : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]][
      "CompositeTypes"
    ]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][
    CompositeTypeName
  ]
  : PublicCompositeTypeNameOrOptions extends
    keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never;

// User role type definition
export type UserRole = "patient" | "caregiver" | "healthcare_provider";

export const Constants = {
  public: {
    Enums: {},
  },
} as const;
