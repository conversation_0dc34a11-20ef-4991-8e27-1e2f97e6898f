import * as z from "zod/v4";
import type { UserRole } from "./database.types";

// Auth validation schemas
export const signUpSchema = z.object({
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address"),
  password: z
    .string()
    .min(6, "Password must be at least 6 characters long")
    .max(100, "Password is too long"),
  confirmPassword: z
    .string()
    .min(1, "Please confirm your password"),
  fullName: z
    .string()
    .min(1, "Full name is required")
    .max(100, "Full name is too long")
    .regex(/^[a-zA-Z\s'-]+$/, "Full name can only contain letters, spaces, hyphens, and apostrophes"),
  role: z.enum(["patient", "caregiver", "healthcare_provider"], {
    errorMap: () => ({ message: "Please select a valid role" }),
  }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

export const signInSchema = z.object({
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address"),
  password: z
    .string()
    .min(1, "Password is required"),
});

export const forgotPasswordSchema = z.object({
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address"),
});

export const resetPasswordSchema = z.object({
  password: z
    .string()
    .min(6, "Password must be at least 6 characters long")
    .max(100, "Password is too long"),
  confirmPassword: z
    .string()
    .min(1, "Please confirm your password"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

// TypeScript types
export type SignUpFormData = z.infer<typeof signUpSchema>;
export type SignInFormData = z.infer<typeof signInSchema>;
export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;

// Role options for UI components
export const ROLE_OPTIONS = [
  { label: "Patient", value: "patient" as UserRole },
  { label: "Caregiver", value: "caregiver" as UserRole },
  { label: "Healthcare Provider", value: "healthcare_provider" as UserRole },
] as const;

// Validation error types
export interface ValidationErrors {
  [key: string]: string | undefined;
}

// Form state interface
export interface FormState<T> {
  data: T;
  errors: ValidationErrors;
  isValid: boolean;
  isDirty: boolean;
}
