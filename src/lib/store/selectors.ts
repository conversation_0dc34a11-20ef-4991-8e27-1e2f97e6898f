import { useCallback, useEffect } from "react";
import { useShallow } from "zustand/react/shallow";
import { useAppStore } from "./index";

// ============================================================================
// GRANULAR SELECTORS - Use these instead of large slice selectors
// ============================================================================

// Auth selectors
export const useAuthUser = () => useAppStore((state) => state.user);
export const useIsAuthenticated = () =>
  useAppStore((state) => state.isAuthenticated);
export const useAuthLoading = () => useAppStore((state) => state.isLoading);
export const useAuthError = () => useAppStore((state) => state.error);
export const useHasCompletedOnboarding = () =>
  useAppStore((state) => state.hasCompletedOnboarding);

// Auth actions (grouped for related operations)
export const useAuthActions = () =>
  useAppStore(
    useShallow((state) => ({
      login: state.login,
      logout: state.logout,
      updateUser: state.updateUser,
      setOnboardingComplete: state.setOnboardingComplete,
      clearError: state.clearError,
    })),
  );

// Nutrition selectors
export const useFoodLogs = () => useAppStore((state) => state.foodLogs);
export const useDailyMetrics = () => useAppStore((state) => state.dailyMetrics);
export const useNutritionLoading = () =>
  useAppStore((state) => state.isLoading);
export const useNutritionError = () => useAppStore((state) => state.error);

// Nutrition actions
export const useNutritionActions = () =>
  useAppStore(
    useShallow((state) => ({
      addFoodLog: state.addFoodLog,
      removeFoodLog: state.removeFoodLog,
      updateFoodLog: state.updateFoodLog,
      addHealthMetric: state.addHealthMetric,
      searchFoodDatabase: state.searchFoodDatabase,
    })),
  );

// Computed nutrition selectors (memoized)
export const useDailyNutrition = (date?: Date) =>
  useAppStore(
    (state) => state.getDailyNutrition(date),
  );

export const useMealLogs = (mealType: string, date?: Date) =>
  useAppStore(
    (state) => state.getMealLogs(mealType, date),
  );

// Medication selectors
export const useMedications = () => useAppStore((state) => state.medications);
export const useReminders = () => useAppStore((state) => state.reminders);
export const useMedicationLoading = () =>
  useAppStore((state) => state.isLoading);
export const useMedicationError = () => useAppStore((state) => state.error);

// Medication actions
export const useMedicationActions = () =>
  useAppStore(
    useShallow((state) => ({
      addMedication: state.addMedication,
      updateMedication: state.updateMedication,
      removeMedication: state.removeMedication,
      addReminder: state.addReminder,
      markReminderTaken: state.markReminderTaken,
    })),
  );

// Computed medication selectors
export const useTodaysReminders = () =>
  useAppStore((state) => state.getTodaysReminders());
export const useUpcomingReminders = () =>
  useAppStore((state) => state.getUpcomingReminders());

// Settings selectors
export const useSettings = () => useAppStore((state) => state.settings);
export const useSettingsLoading = () => useAppStore((state) => state.isLoading);
export const useSettingsError = () => useAppStore((state) => state.error);

// Individual settings selectors (for components that only need specific settings)
export const useThemePreference = () =>
  useAppStore((state) => state.settings.themePreference);
export const useNotificationSettings = () =>
  useAppStore((state) => state.settings.notifications);
export const useLanguage = () =>
  useAppStore((state) => state.settings.language);
export const useUnits = () => useAppStore((state) => state.settings.units);

// Settings actions
export const useSettingsActions = () =>
  useAppStore(
    useShallow((state) => ({
      updateThemePreference: state.updateThemePreference,
      updateNotificationSettings: state.updateNotificationSettings,
      updateLanguage: state.updateLanguage,
      updateUnits: state.updateUnits,
      resetSettings: state.resetSettings,
      loadSettings: state.loadSettings,
      saveSettings: state.saveSettings,
    })),
  );

// Theme selectors
export const useTheme = () => useAppStore((state) => state.theme);
export const useThemeColors = () => useAppStore((state) => state.theme.colors);
export const useIsDarkTheme = () => useAppStore((state) => state.isDark);
export const useThemeMode = () => useAppStore((state) => state.themeMode);
export const useSystemColorScheme = () =>
  useAppStore((state) => state.systemColorScheme);

// Theme actions
export const useThemeActions = () =>
  useAppStore(
    useShallow((state) => ({
      setThemeMode: state.setThemeMode,
      setSystemColorScheme: state.setSystemColorScheme,
      toggleTheme: state.toggleTheme,
      initializeTheme: state.initializeTheme,
    })),
  );

// UI selectors
export const useIsPageVisible = () =>
  useAppStore((state) => state.isPageVisible);
export const useUILoading = () => useAppStore((state) => state.isLoading);
export const useProgress = () => useAppStore((state) => state.progress);
export const useActiveTab = () => useAppStore((state) => state.activeTab);
export const useModals = () => useAppStore((state) => state.modals);
export const useNotifications = () =>
  useAppStore((state) => state.notifications);

// Individual modal selectors (for components that only care about specific modals)
export const useAddFoodModal = () =>
  useAppStore((state) => state.modals.addFood);
export const useCameraModal = () => useAppStore((state) => state.modals.camera);
export const useSettingsModal = () =>
  useAppStore((state) => state.modals.settings);

// UI actions
export const useUIActions = () =>
  useAppStore(
    useShallow((state) => ({
      setPageVisible: state.setPageVisible,
      setLoading: state.setLoading,
      setProgress: state.setProgress,
      setActiveTab: state.setActiveTab,
      openModal: state.openModal,
      closeModal: state.closeModal,
      closeAllModals: state.closeAllModals,
      addNotification: state.addNotification,
      removeNotification: state.removeNotification,
      clearNotifications: state.clearNotifications,
    })),
  );

// ============================================================================
// COMPUTED SELECTORS - Complex derived state
// ============================================================================

// Daily progress with memoization
export const useDailyProgress = () =>
  useAppStore(
    useShallow((state) => {
      const nutrition = state.getDailyNutrition();
      return {
        sodium: nutrition.sodium,
        protein: nutrition.protein,
        fat: nutrition.fat,
        calories: nutrition.calories,
        water: 6, // This would come from water tracking when implemented
      };
    }),
  );

// Today's medications with full details
export const useTodaysMedications = () =>
  useAppStore(
    useShallow((state) => {
      return state.getTodaysReminders().map((reminder) => {
        const medication = state.medications.find((med) =>
          med.id === reminder.medicationId
        );
        return {
          ...reminder,
          medication,
        };
      });
    }),
  );

// Latest notification (for toast displays)
export const useLatestNotification = () =>
  useAppStore(
    (state) =>
      state.notifications.length > 0
        ? state.notifications[state.notifications.length - 1]
        : null,
  );

// ============================================================================
// PERFORMANCE MONITORING SELECTORS
// ============================================================================

// Store size monitoring
export const useStoreSize = () =>
  useAppStore((state) => ({
    foodLogs: state.foodLogs.length,
    dailyMetrics: state.dailyMetrics.length,
    medications: state.medications.length,
    notifications: state.notifications.length,
  }));

// Store performance monitoring
export const useStorePerformance = () =>
  useAppStore((state) => {
    const stateString = JSON.stringify(state);
    return {
      bytes: stateString.length,
      kb: Math.round(stateString.length / 1024),
    };
  });

// ============================================================================
// MEMOIZED COMPUTED SELECTORS - Optimized with useCallback
// ============================================================================

// Memoized daily nutrition with date parameter
export const useDailyNutritionMemo = (date?: Date) => {
  return useAppStore(
    useCallback((state) => state.getDailyNutrition(date), [date]),
  );
};

// Meal type constants and type
export const MealType = {
  breakfast: "breakfast",
  lunch: "lunch",
  dinner: "dinner",
  snack: "snack",
} as const;

export type MealType = (typeof MealType)[keyof typeof MealType];

// Memoized meal logs with parameters
export const useMealLogsMemo = (mealType: MealType, date?: Date) => {
  return useAppStore(
    useCallback((state) => state.getMealLogs(mealType, date), [mealType, date]),
  );
};

// ============================================================================
// BUSINESS LOGIC HOOKS - Complex operations and validation
// ============================================================================

// Theme synchronization hook
export const useThemeSync = () => {
  const { settings, setThemeMode } = useAppStore(
    useShallow((state) => ({
      settings: state.settings,
      setThemeMode: state.setThemeMode,
    })),
  );

  useEffect(() => {
    // Sync theme mode with settings
    setThemeMode(settings.themePreference);
  }, [settings.themePreference, setThemeMode]);
};

// Data cleanup operations
export const useDataCleanup = () => {
  const { foodLogs, dailyMetrics, notifications } = useAppStore(
    useShallow((state) => ({
      foodLogs: state.foodLogs,
      dailyMetrics: state.dailyMetrics,
      notifications: state.notifications,
    })),
  );

  const cleanupOldData = useCallback(() => {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const oneHourAgo = new Date();
    oneHourAgo.setHours(oneHourAgo.getHours() - 1);

    const recentFoodLogs = foodLogs.filter(
      (log) => log.timestamp >= thirtyDaysAgo,
    );
    const recentMetrics = dailyMetrics.filter(
      (metric) => metric.date >= thirtyDaysAgo,
    );
    const recentNotifications = notifications.filter(
      (notification) => notification.timestamp >= oneHourAgo,
    );

    useAppStore.setState({
      foodLogs: recentFoodLogs,
      dailyMetrics: recentMetrics,
      notifications: recentNotifications,
    });
  }, [foodLogs, dailyMetrics, notifications]);

  return { cleanupOldData };
};

// Batch operations for performance
export const useBatchOperations = () => {
  const store = useAppStore();

  const batchAddFoodLogs = useCallback(
    (
      foodItems: {
        foodItem: any;
        mealType: MealType;
      }[],
    ) => {
      foodItems.forEach(({ foodItem, mealType }) => {
        store.addFoodLog(foodItem, mealType);
      });
    },
    [store],
  );

  const batchUpdateSettings = useCallback(
    (updates: Record<string, any>) => {
      Object.entries(updates).forEach(([key, value]) => {
        switch (key) {
          case "theme":
            store.updateThemePreference(value);
            store.setThemeMode(value);
            break;
          case "notifications":
            store.updateNotificationSettings(value);
            break;
          case "language":
            store.updateLanguage(value);
            break;
          case "units":
            store.updateUnits(value);
            break;
        }
      });
    },
    [store],
  );

  return {
    batchAddFoodLogs,
    batchUpdateSettings,
  };
};

// Food validation with liver health checks
export const useValidation = () => {
  const addFoodWithValidation = useCallback(
    (foodItem: any, mealType: MealType) => {
      if (!foodItem || !mealType) {
        throw new Error("Food item and meal type are required");
      }

      const store = useAppStore.getState();

      // Check liver-friendliness
      if (!foodItem.isLiverFriendly && foodItem.warnings?.length > 0) {
        store.addNotification({
          type: "warning",
          message: `${foodItem.name} may not be suitable for liver health. ${
            foodItem.warnings.join(
              ". ",
            )
          }`,
        });
      }

      store.addFoodLog(foodItem, mealType);

      // Check daily limits
      const dailyNutrition = store.getDailyNutrition();
      if (dailyNutrition.sodium > 2300) {
        store.addNotification({
          type: "warning",
          message:
            "Daily sodium limit exceeded. Consider reducing sodium intake.",
        });
      }

      store.addNotification({
        type: "success",
        message: `${foodItem.name} added to ${mealType}`,
      });
    },
    [],
  );

  return { addFoodWithValidation };
};

// Development and debugging tools
export const useDevTools = () => {
  const logState = useCallback(() => {
    console.log("Current store state:", useAppStore.getState());
  }, []);

  const logPerformance = useCallback(() => {
    const state = useAppStore.getState();
    const stateSize = JSON.stringify(state).length;
    console.log("Store performance:", {
      sizeInBytes: stateSize,
      sizeInKB: Math.round(stateSize / 1024),
      foodLogs: state.foodLogs.length,
      dailyMetrics: state.dailyMetrics.length,
      medications: state.medications.length,
      notifications: state.notifications.length,
    });
  }, []);

  return {
    logState,
    logPerformance,
  };
};

// ============================================================================
// LEGACY SELECTORS - Deprecated, use granular selectors above
// ============================================================================

// Keep these for backward compatibility but mark as deprecated
/** @deprecated Use granular auth selectors instead */
export const useAuth = () =>
  useAppStore(
    useShallow((state) => ({
      user: state.user,
      isAuthenticated: state.isAuthenticated,
      isLoading: state.isLoading,
      error: state.error,
      hasCompletedOnboarding: state.hasCompletedOnboarding,
      login: state.login,
      logout: state.logout,
      updateUser: state.updateUser,
      setOnboardingComplete: state.setOnboardingComplete,
      clearError: state.clearError,
    })),
  );

/** @deprecated Use granular nutrition selectors instead */
export const useNutrition = () =>
  useAppStore(
    useShallow((state) => ({
      foodLogs: state.foodLogs,
      dailyMetrics: state.dailyMetrics,
      isLoading: state.isLoading,
      error: state.error,
      addFoodLog: state.addFoodLog,
      removeFoodLog: state.removeFoodLog,
      updateFoodLog: state.updateFoodLog,
      addHealthMetric: state.addHealthMetric,
      getDailyNutrition: state.getDailyNutrition,
      getMealLogs: state.getMealLogs,
      searchFoodDatabase: state.searchFoodDatabase,
    })),
  );
