import { useCallback, useState } from "react";
import { Alert } from "react-native";
import { router } from "expo-router";
import {
  type SignUpFormData,
  signUpSchema,
  type ValidationErrors,
} from "@/types/auth";
import { useAuth } from "@/lib/contexts/AuthContext";

interface UseSignUpFormReturn {
  formData: SignUpFormData;
  errors: ValidationErrors;
  loading: boolean;
  isValid: boolean;
  updateField: (field: keyof SignUpFormData, value: string) => void;
  handleSubmit: () => Promise<void>;
  validateField: (field: keyof SignUpFormData) => void;
  validateForm: () => boolean;
  clearErrors: () => void;
}

export function useSignUpForm(): UseSignUpFormReturn {
  const [formData, setFormData] = useState<SignUpFormData>({
    email: "",
    password: "",
    confirmPassword: "",
    fullName: "",
    role: "patient",
  });

  const [errors, setErrors] = useState<ValidationErrors>({});
  const [loading, setLoading] = useState(false);
  const { signUp } = useAuth();

  // Update a single field
  const updateField = useCallback(
    (field: keyof SignUpFormData, value: string) => {
      setFormData((prev) => ({ ...prev, [field]: value }));

      // Clear field error when user starts typing
      if (errors[field]) {
        setErrors((prev) => ({ ...prev, [field]: undefined }));
      }
    },
    [errors],
  );

  // Validate a single field
  const validateField = useCallback((field: keyof SignUpFormData) => {
    try {
      // Create a partial schema for the specific field
      const fieldSchema = signUpSchema.pick({ [field]: true });
      fieldSchema.parse({ [field]: formData[field] });

      // Clear error if validation passes
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    } catch (error: any) {
      if (error.errors && error.errors.length > 0) {
        setErrors((prev) => ({ ...prev, [field]: error.errors[0].message }));
      }
    }
  }, [formData]);

  // Validate entire form
  const validateForm = useCallback((): boolean => {
    try {
      signUpSchema.parse(formData);
      setErrors({});
      return true;
    } catch (error: any) {
      if (error.errors) {
        const newErrors: ValidationErrors = {};
        error.errors.forEach((err: any) => {
          const field = err.path[0];
          if (field) {
            newErrors[field] = err.message;
          }
        });
        setErrors(newErrors);
      }
      return false;
    }
  }, [formData]);

  // Clear all errors
  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const { error } = await signUp(formData.email, formData.password, {
        full_name: formData.fullName,
        role: formData.role,
      });

      if (error) {
        Alert.alert("Sign Up Failed", error.message);
      } else {
        Alert.alert(
          "Success",
          "Account created! Please check your email for verification.",
          [{ text: "OK", onPress: () => router.replace("/(auth)/sign-in") }],
        );
      }
    } catch (error) {
      console.error("Sign up error:", error);
      Alert.alert("Error", "An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  }, [formData, signUp, validateForm]);

  // Check if form is valid
  const isValid = Object.keys(errors).length === 0 &&
    formData.email.length > 0 &&
    formData.password.length > 0 &&
    formData.fullName.length > 0;

  return {
    formData,
    errors,
    loading,
    isValid,
    updateField,
    handleSubmit,
    validateField,
    validateForm,
    clearErrors,
  };
}
