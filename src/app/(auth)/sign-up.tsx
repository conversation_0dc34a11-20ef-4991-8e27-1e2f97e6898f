import React from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { Link } from 'expo-router';
import { Picker } from '@react-native-picker/picker';
import { useThemeColors } from '@/lib/store/selectors';
import { useSignUpForm } from '@/lib/hooks/useSignUpForm';
import { ROLE_OPTIONS } from '@/types/auth';

export default function SignUpScreen() {
  const colors = useThemeColors();
  const {
    formData,
    errors,
    loading,
    isValid,
    updateField,
    handleSubmit,
    validateField,
  } = useSignUpForm();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    scrollView: {
      flex: 1,
    },
    content: {
      paddingHorizontal: 24,
      paddingVertical: 40,
    },
    title: {
      fontSize: 32,
      fontWeight: 'bold',
      color: colors.text,
      marginBottom: 8,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: 16,
      color: colors.textSecondary,
      marginBottom: 32,
      textAlign: 'center',
    },
    input: {
      backgroundColor: colors.surface,
      borderRadius: 12,
      padding: 16,
      fontSize: 16,
      color: colors.text,
      marginBottom: 8,
      borderWidth: 1,
      borderColor: colors.border,
    },
    inputError: {
      borderColor: colors.error,
      borderWidth: 2,
    },
    errorText: {
      fontSize: 12,
      marginBottom: 16,
      marginTop: 4,
      marginLeft: 4,
    },
    pickerContainer: {
      backgroundColor: colors.surface,
      borderRadius: 12,
      marginBottom: 8,
      borderWidth: 1,
      borderColor: colors.border,
      padding: 16,
      fontSize: 16,
      color: colors.textSecondary,
    },
    picker: {
      color: colors.text,
    },
    label: {
      fontSize: 14,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 8,
      marginLeft: 4,
    },
    button: {
      backgroundColor: colors.primary,
      borderRadius: 12,
      padding: 16,
      alignItems: 'center',
      marginTop: 8,
      marginBottom: 24,
    },
    buttonDisabled: {
      opacity: 0.6,
    },
    buttonText: {
      color: 'white',
      fontSize: 16,
      fontWeight: '600',
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    footerText: {
      color: colors.textSecondary,
      fontSize: 14,
    },
    link: {
      color: colors.primary,
      fontSize: 14,
      fontWeight: '600',
    },
  });

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.scrollView} keyboardShouldPersistTaps="handled">
        <View style={styles.content}>
          <Text style={styles.title}>Create Account</Text>
          <Text style={styles.subtitle}>
            Join us in your liver health journey
          </Text>

          <TextInput
            style={[styles.input, errors.fullName && styles.inputError]}
            placeholder="Full Name"
            placeholderTextColor={colors.textSecondary}
            value={formData.fullName}
            onChangeText={text => updateField('fullName', text)}
            onBlur={() => validateField('fullName')}
            autoCapitalize="words"
            autoComplete="name"
          />
          {errors.fullName && (
            <Text style={[styles.errorText, { color: colors.error }]}>
              {errors.fullName}
            </Text>
          )}

          <TextInput
            style={[styles.input, errors.email && styles.inputError]}
            placeholder="Email"
            placeholderTextColor={colors.textSecondary}
            value={formData.email}
            onChangeText={text => updateField('email', text)}
            onBlur={() => validateField('email')}
            keyboardType="email-address"
            autoCapitalize="none"
            autoComplete="email"
          />
          {errors.email && (
            <Text style={[styles.errorText, { color: colors.error }]}>
              {errors.email}
            </Text>
          )}

          <Text style={styles.label}>Role</Text>
          <View
            style={[styles.pickerContainer, errors.role && styles.inputError]}
          >
            <Picker
              selectedValue={formData.role}
              style={styles.picker}
              onValueChange={value => updateField('role', value)}
            >
              {ROLE_OPTIONS.map(option => (
                <Picker.Item
                  key={option.value}
                  label={option.label}
                  value={option.value}
                />
              ))}
            </Picker>
          </View>
          {errors.role && (
            <Text style={[styles.errorText, { color: colors.error }]}>
              {errors.role}
            </Text>
          )}

          <TextInput
            style={[styles.input, errors.password && styles.inputError]}
            placeholder="Password"
            placeholderTextColor={colors.textSecondary}
            value={formData.password}
            onChangeText={text => updateField('password', text)}
            onBlur={() => validateField('password')}
            secureTextEntry
            autoComplete="new-password"
          />
          {errors.password && (
            <Text style={[styles.errorText, { color: colors.error }]}>
              {errors.password}
            </Text>
          )}

          <TextInput
            style={[styles.input, errors.confirmPassword && styles.inputError]}
            placeholder="Confirm Password"
            placeholderTextColor={colors.textSecondary}
            value={formData.confirmPassword}
            onChangeText={text => updateField('confirmPassword', text)}
            onBlur={() => validateField('confirmPassword')}
            secureTextEntry
            autoComplete="new-password"
          />
          {errors.confirmPassword && (
            <Text style={[styles.errorText, { color: colors.error }]}>
              {errors.confirmPassword}
            </Text>
          )}

          <TouchableOpacity
            style={[
              styles.button,
              (loading || !isValid) && styles.buttonDisabled,
            ]}
            onPress={handleSubmit}
            disabled={loading || !isValid}
          >
            <Text style={styles.buttonText}>
              {loading ? 'Creating Account...' : 'Create Account'}
            </Text>
          </TouchableOpacity>

          <View style={styles.footer}>
            <Text style={styles.footerText}>Already have an account? </Text>
            <Link href="/(auth)/sign-in" asChild>
              <TouchableOpacity>
                <Text style={styles.link}>Sign In</Text>
              </TouchableOpacity>
            </Link>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
